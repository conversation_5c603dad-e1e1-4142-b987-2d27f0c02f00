package cn.tianxing.cloud.module.crm.service.customer.impl;

import cn.hutool.core.collection.CollUtil;
import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.common.util.object.BeanUtils;
import cn.tianxing.cloud.module.crm.controller.admin.customer.dto.CustomerCollectDetailSaveDTO;
import cn.tianxing.cloud.module.crm.controller.admin.customer.dto.CustomerCollectQueryDTO;
import cn.tianxing.cloud.module.crm.controller.admin.customer.dto.CustomerCollectSaveDTO;
import cn.tianxing.cloud.module.crm.controller.admin.customer.vo.CustomerCollectVO;
import cn.tianxing.cloud.module.crm.convert.customer.CustomerConvert;
import cn.tianxing.cloud.module.crm.convert.customer.CustomerDetailConvert;
import cn.tianxing.cloud.module.crm.dal.dataobject.customer.CustomerCollectDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.customer.CustomerCollectDetailDO;
import cn.tianxing.cloud.module.crm.dal.mysql.customer.CustomerCollectDetailMapper;
import cn.tianxing.cloud.module.crm.dal.mysql.customer.CustomerCollectMapper;
import cn.tianxing.cloud.module.crm.dal.redis.no.CrmNoRedisDAO;
import cn.tianxing.cloud.module.crm.enums.ErrorCodeConstants;
import cn.tianxing.cloud.module.crm.service.customer.ICustomerCollectService;
import cn.tianxing.cloud.module.system.api.ip.IpApi;
import cn.tianxing.cloud.module.system.api.user.AdminUserApi;
import cn.tianxing.cloud.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.tianxing.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.tianxing.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;


/**
 * <p>
 * 信息收集表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Service
@Slf4j
@RefreshScope
public class CustomerCollectServiceImpl extends ServiceImpl<CustomerCollectMapper, CustomerCollectDO> implements ICustomerCollectService {

    @Resource
    private CustomerCollectMapper customerCollectMapper;

    @Resource
    private CustomerCollectDetailMapper customerCollectDetailMapper;

    @Resource
    private CrmNoRedisDAO crmNoRedisDAO;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private IpApi ipApi;

    @Value("${userInfo.userId}")
    private Long adminUser;

    /**
     * 创建客户信息
     *
     * @param collectSaveDTO 客户信息
     * @return 信息收集编号
     */
    @Override
    @Transactional
    public Long createCustomerCollec(CustomerCollectSaveDTO collectSaveDTO) {
        // 1.添加客户信息
        // 客户编号，自动生成
        String customerNum = crmNoRedisDAO.generateUserCode();
        CustomerCollectDO customerCollectDO = BeanUtils.toBean(collectSaveDTO, CustomerCollectDO.class);
        customerCollectDO.setCustomerNum(customerNum);
        customerCollectDO.setId(null);

        // 设置省市区
        CommonResult<String> provinces = ipApi.getProvinces(collectSaveDTO.getAddressProvince(), collectSaveDTO.getAddressCity(), collectSaveDTO.getAddressCode());
        customerCollectDO.setAddressName(provinces.getData());
        customerCollectMapper.insert(customerCollectDO);

        // 2.添加客户信息详情
        Long customerCollectId = customerCollectDO.getId();
        List<CustomerCollectDetailSaveDTO> creditInfoList = collectSaveDTO.getCreditInfoList();
        if (CollUtil.isNotEmpty(creditInfoList)) {
            LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
            String userId = getLoginUserId().toString();
            insertCustomerCollectDetail(customerCollectId, userId, now, creditInfoList);
        }

        return customerCollectId;
    }

    /**
     * @param customerCollectId 客户信息ID
     * @param userId            当前用户
     * @param now               当前时间
     * @param creditInfoList    客户信息详情列表
     */
    private void insertCustomerCollectDetail(Long customerCollectId, String userId, LocalDateTime now, List<CustomerCollectDetailSaveDTO> creditInfoList) {
        List<CustomerCollectDetailDO> customerCollectDetailList = new ArrayList<>();
        // 2.1.客户信息详情转换DO
        for (CustomerCollectDetailSaveDTO customerCollectDetailSaveDTO : creditInfoList) {
            CustomerCollectDetailDO customerCollectDetailDO = BeanUtils.toBean(customerCollectDetailSaveDTO, CustomerCollectDetailDO.class);
            customerCollectDetailDO.setCustomerCollectId(customerCollectId);
            customerCollectDetailDO.setId(null);
            customerCollectDetailDO.setCreator(userId);
            customerCollectDetailDO.setUpdater(userId);
            customerCollectDetailDO.setCreateTime(now);
            customerCollectDetailDO.setUpdateTime(now);
            customerCollectDetailList.add(customerCollectDetailDO);
        }
        // 2.2.批量添加
        customerCollectDetailMapper.insert(customerCollectDetailList);
    }

    /**
     * 获取客户信息收集分页列表
     *
     * @param collectQueryDTO 分页条件
     * @return 分页列表
     */
    @Override
    public PageResult<CustomerCollectVO> getCustomerCollecPage(CustomerCollectQueryDTO collectQueryDTO) {
        IPage<CustomerCollectVO> page = new Page<>(collectQueryDTO.getPageNo(), collectQueryDTO.getPageSize());
        // 获取当前角色
        Long userId = getLoginUserId();
        if (userId.equals(adminUser) || userId.equals(1L)) {
            // 管理员
            customerCollectMapper.getCustomerCollecPageAdmin(page, collectQueryDTO);
        } else {
            // 非管理员
            customerCollectMapper.getCustomerCollecPage(page, collectQueryDTO, userId);
        }
        // 返回Vo
        if (CollUtil.isEmpty(page.getRecords())) {
            return new PageResult<>();
        } else {
            return new PageResult<>(page.getRecords(), page.getTotal());
        }
    }

    /**
     * 获取客户信息收集详情
     *
     * @param id 客户信息收集编号
     * @return 客户信息
     */
    @Override
    public CustomerCollectVO getCustomerCollec(Long id) {
        CustomerCollectDO customerCollectDO = customerCollectMapper.selectById(id);
        if (Objects.nonNull(customerCollectDO) && !customerCollectDO.getDeleted()) {
            // 1.获取客户信息
            CustomerCollectVO customerCollectVO = CustomerConvert.INSTANCE.convert(customerCollectDO);
            String creator = customerCollectDO.getCreator();
            if (!creator.isEmpty()) {
                // 1.1.获取创建人名称
                CommonResult<AdminUserRespDTO> user = adminUserApi.getUser(Long.valueOf(creator));
                if (Objects.nonNull(user)) {
                    String nickname = user.getData() != null ? user.getData().getNickname() : "";
                    customerCollectVO.setCreatorName(nickname);
                }
            }
            // 2.获取债权/债务信息
            Long customerCollectId = customerCollectVO.getId();
            List<CustomerCollectDetailDO> customerCollectDetailDOList = customerCollectDetailMapper.selectBycustomerCollectId(customerCollectId);
            if (CollUtil.isNotEmpty(customerCollectDetailDOList)) {
                List<CustomerCollectDetailSaveDTO> customerCollectDetailSaveDTOS = CustomerDetailConvert.INSTANCE.convertList(customerCollectDetailDOList);
                customerCollectVO.setCreditInfoList(customerCollectDetailSaveDTOS);
            }
            // 3.返回Vo
            return customerCollectVO;
        } else {
            throw exception(ErrorCodeConstants.CUSTOMER_NOT_NULL);
        }
    }

    /**
     * 修改客户信息收集
     *
     * @param collectSaveDTO 客户信息
     */
    @Override
    public void updateCustomerCollec(CustomerCollectSaveDTO collectSaveDTO) {
        // 1.客户信息校验
        Long customerCollectId = collectSaveDTO.getId();
        if (Objects.isNull(customerCollectId)) {
            throw exception(ErrorCodeConstants.CUSTOMER_ID_IS_NOT_NULL);
        }
        getCustomerCollec(customerCollectId);

        // 2.修改客户信息
        CustomerCollectDO customerCollectDO = BeanUtils.toBean(collectSaveDTO, CustomerCollectDO.class);
        // 设置省市区
        CommonResult<String> provinces = ipApi.getProvinces(collectSaveDTO.getAddressProvince(), collectSaveDTO.getAddressCity(), collectSaveDTO.getAddressCode());
        customerCollectDO.setAddressName(provinces.getData());
        customerCollectMapper.updateById(customerCollectDO);
        
        // 3.修改债权/债务信息
        // 删除
        String userId = getLoginUserId().toString();
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        customerCollectDetailMapper.updateByCustomerCollecId(customerCollectId, userId, now);
        // 修改
        List<CustomerCollectDetailSaveDTO> creditInfoList = collectSaveDTO.getCreditInfoList();
        if (CollUtil.isNotEmpty(creditInfoList)) {
            insertCustomerCollectDetail(customerCollectId, userId, now, creditInfoList);
        }
    }

    @Override
    @Transactional
    public void deleteCustomerCollec(Long id) {
        // 1.删除信息收集表授权表，软删除
        String userId = getLoginUserId().toString();
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        customerCollectDetailMapper.updateByCustomerCollecId(id, userId, now);
        // 2.删除客户信息收集表，软删除
        CustomerCollectDO customerCollectDO = new CustomerCollectDO();
        customerCollectDO.setId(id);
        customerCollectDO.setDeleted(true);
        customerCollectMapper.updateById(customerCollectDO);
    }

}
