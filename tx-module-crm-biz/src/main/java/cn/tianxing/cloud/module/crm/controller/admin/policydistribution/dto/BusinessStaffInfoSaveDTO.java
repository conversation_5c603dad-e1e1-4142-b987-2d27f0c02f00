package cn.tianxing.cloud.module.crm.controller.admin.policydistribution.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 业务员管理 - 业务员保存DTO")
@Data
public class BusinessStaffInfoSaveDTO {

    @Schema(description = "账号类型（来自字典 business_staff_account_type）")
    @NotBlank(message = "账号类型不能为空")
    private String accountType;

    @Schema(description = "业务员ID")
    @NotNull(message = "业务员ID不能为空")
    private Long businessStaffId;

    @Schema(description = "业务范围")
    private String businessScope;

    @Schema(description = "业务范围明细")
    private List<String> businessScopeDetail;
    
}
