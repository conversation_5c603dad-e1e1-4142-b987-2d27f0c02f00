package cn.tianxing.cloud.module.crm.service.customer.impl;


import cn.hutool.core.util.StrUtil;
import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.crm.controller.admin.customer.dto.PermissionCollectQueryDTO;
import cn.tianxing.cloud.module.crm.controller.admin.customer.dto.PermissionOperateDTO;
import cn.tianxing.cloud.module.crm.dal.dataobject.customer.CustomerCollectDO;
import cn.tianxing.cloud.module.crm.dal.dataobject.customer.CustomerCollectPermissionDO;
import cn.tianxing.cloud.module.crm.dal.mysql.customer.CustomerCollectPermissionMapper;
import cn.tianxing.cloud.module.crm.enums.ErrorCodeConstants;
import cn.tianxing.cloud.module.crm.service.customer.CustomerCollectPermissionService;
import cn.tianxing.cloud.module.crm.service.customer.ICustomerCollectService;
import cn.tianxing.cloud.module.system.api.user.AdminUserApi;
import cn.tianxing.cloud.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static cn.tianxing.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.tianxing.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * <AUTHOR>
 * @description 针对表【customer_collect_permission(信息收集表授权表)】的数据库操作Service实现
 * @createDate 2025-04-28 13:53:10
 */
@Service
@RefreshScope
public class CustomerCollectPermissionServiceImpl extends ServiceImpl<CustomerCollectPermissionMapper, CustomerCollectPermissionDO>
        implements CustomerCollectPermissionService {

    @Resource
    private ICustomerCollectService customerCollectService;

    @Resource
    private AdminUserApi adminUserApi;

    @Value("${userInfo.userId}")
    private Long userId;

    @Override
    @Transactional
    public void create(Long loginUserId, List<Long> ids) {
        List<CustomerCollectPermissionDO> saveList = new ArrayList<>();
        for (Long id : ids) {
            CustomerCollectDO customerCollectDO = customerCollectService.getById(id);
            CustomerCollectPermissionDO permissionDO = new CustomerCollectPermissionDO();
            permissionDO.setCustomerCollectId(customerCollectDO.getId());
            permissionDO.setStatus("0");
            permissionDO.setTaskType("客户信息");
            permissionDO.setTaskName("客户信息查看");
            permissionDO.setTaskContent(prepareTaskContent(customerCollectDO));
            permissionDO.setCreateTime(new Date());
            permissionDO.setUpdateTime(new Date());
            permissionDO.setCreator(loginUserId.toString());
            permissionDO.setUpdater(loginUserId.toString());
            permissionDO.setDeleted(0);
            permissionDO.setApplyId(loginUserId);
            permissionDO.setApprovalId(userId);
            saveList.add(permissionDO);
        }
        saveBatch(saveList);
    }

    @Override
    public void approval(Long loginUserId, PermissionOperateDTO dto) {
        // 1.权限校验
        if (!userId.equals(loginUserId)) {
            throw exception(ErrorCodeConstants.CUSTOMER_PERMISSION_NO_ACCESS);
        }
        // 2.审评
        CustomerCollectPermissionDO byId = getById(dto.getId());
        if (Objects.isNull(byId)){
            throw exception(ErrorCodeConstants.CUSTOMER_PERMISSION_NOT_EXISTS);
        }
        byId.setStatus("1");
        byId.setPassFlag(dto.getPassFlag());
        this.updateById(byId);
    }

    private String prepareTaskContent(CustomerCollectDO customerCollectDO) {
        return "客户姓名：" + customerCollectDO.getCustomerName() + "，客户编号：" + customerCollectDO.getCustomerNum();
    }

    public PageResult<CustomerCollectPermissionDO> queryPermission(PermissionCollectQueryDTO query, Page<CustomerCollectPermissionDO> page) {
        Long loginUserId = getLoginUserId();
        if (StrUtil.isEmpty(query.getApplicant())){
            query.setApplicant(loginUserId.toString());
        }
        PageResult<CustomerCollectPermissionDO> doPage = this.baseMapper.selectPage(query);
        List<Long> userIds = doPage.getList().stream()
                .map(CustomerCollectPermissionDO::getApplyId)
                .collect(Collectors.toList());

        userIds.addAll(doPage.getList().stream()
                .map(CustomerCollectPermissionDO::getApprovalId)
                .toList());
        List<AdminUserRespDTO> adminUsers = adminUserApi.getUserList(userIds).getCheckedData();
        Map<Long, String> userMap = adminUsers.stream()
                .collect(Collectors.toMap(
                        AdminUserRespDTO::getId,
                        AdminUserRespDTO::getNickname
                ));
        doPage.getList().forEach(item -> {
            item.setApplyName(userMap.get(item.getApplyId()));
            item.setApprovalName(userMap.get(item.getApprovalId()));
        });
        return doPage;
    }
}




