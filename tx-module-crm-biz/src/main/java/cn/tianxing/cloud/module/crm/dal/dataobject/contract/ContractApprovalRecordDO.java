package cn.tianxing.cloud.module.crm.dal.dataobject.contract;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 合同审批记录表
 *
 * @TableName contract_approval_record
 */
@TableName(value = "contract_approval_record")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContractApprovalRecordDO implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime approvalTime;

    /**
     * 合同状态
     * 0, 草稿
     * 1, 审批中
     * 2, 审批通过
     * 3, 审批驳回
     * 4, 作废
     * 5, 履约中
     * 6, 结束
     * 7, 变更
     */
    @Schema(description = "合同状态: 草稿 draft\n" +
            "审批中 in_approval\n" +
            "审批通过 end_approval\n" +
            "审批驳回 back_approval\n" +
            "作废 cancel\n" +
            "履约中 in_performance\n" +
            "合同结束 end\n" +
            "变更 change")
    private String status;

    /**
     * 审批描述
     */
    private String approvalRemark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "creator", fill = FieldFill.INSERT)
    private String creator;

    /**
     * 更新人
     */
    @TableField(value = "updater", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    @TableField("deleted")
    @TableLogic
    private Boolean deleted;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 