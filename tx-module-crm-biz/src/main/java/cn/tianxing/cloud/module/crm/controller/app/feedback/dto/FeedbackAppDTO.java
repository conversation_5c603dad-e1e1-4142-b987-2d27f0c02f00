package cn.tianxing.cloud.module.crm.controller.app.feedback.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "反馈aPP请求参数")
public class FeedbackAppDTO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "类型 字典")
    private String type;

    @Schema(description = "问题描述")
    private String problem;

    @Schema(description = "联系方式")
    private String contact;

    @Schema(description = "附件")
    private List<String> attachments;
}
