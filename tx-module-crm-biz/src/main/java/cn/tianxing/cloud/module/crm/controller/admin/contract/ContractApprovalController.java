package cn.tianxing.cloud.module.crm.controller.admin.contract;

import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.module.crm.controller.admin.contract.dto.ContractBasicInfoApprovalSavDTO;
import cn.tianxing.cloud.module.crm.controller.admin.contract.vo.ContractApprovalInfoVO;
import cn.tianxing.cloud.module.crm.service.contract.ContractApprovalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 合同管理")
@RestController
@RequestMapping("/crm/contract")
@Validated
public class ContractApprovalController {

    @Resource
    private ContractApprovalService contractApprovalService;

    @PostMapping("/approval")
    @Operation(summary = "合同审批")
    public CommonResult<Boolean> contractApproval(@Valid @RequestBody ContractBasicInfoApprovalSavDTO basicInfoApprovalSavDTO) {
        contractApprovalService.updateBatchContractStatus(basicInfoApprovalSavDTO);
        return success(true);
    }

    @GetMapping("/getApprovalInfo")
    @Operation(summary = "获取合同审批详情")
    public CommonResult<ContractApprovalInfoVO> getApprovalInfo(@RequestParam("id") Long id) {
        return success(contractApprovalService.selectApprovalInfo(id));
    }

}
