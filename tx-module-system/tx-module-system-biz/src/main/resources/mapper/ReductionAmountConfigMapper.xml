<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.tianxing.cloud.module.system.dal.mysql.reduction.ReductionAmountConfigMapper">

    <resultMap id="BaseResultMap" type="cn.tianxing.cloud.module.system.dal.dataobject.reduction.ReductionAmountConfigDO">
        <id property="id" column="id"/>
        <result property="mechanismId" column="mechanism_id"/>
        <result property="period" column="period"/>
        <result property="principalReductionRatio" column="principal_reduction_ratio"/>
        <result property="interestReductionRatio" column="interest_reduction_ratio"/>
        <result property="status" column="status"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, mechanism_id, period, principal_reduction_ratio, interest_reduction_ratio,
        status, creator, create_time, updater, update_time, deleted
    </sql>
</mapper> 