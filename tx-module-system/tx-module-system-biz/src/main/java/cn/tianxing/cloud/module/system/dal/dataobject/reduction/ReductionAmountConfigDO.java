package cn.tianxing.cloud.module.system.dal.dataobject.reduction;

import cn.tianxing.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 减免金额配置 DO
 */
@Schema(description = "减免金额配置")
@TableName("reduction_amount_config")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class ReductionAmountConfigDO extends BaseDO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 机构ID
     */
    @Schema(description = "机构ID")
    private Long mechanismId;

    /**
     * 期数
     */
    @Schema(description = "期数")
    private Long period;

    /**
     * 本金减免比例
     */
    @Schema(description = "本金减免比例")
    private BigDecimal principalReductionRatio;

    /**
     * 利息/罚息减免比例
     */
    @Schema(description = "利息/罚息减免比例")
    private BigDecimal interestReductionRatio;

    /**
     * 状态：0-禁用，1-启用
     */
    @Schema(description = "状态：0-禁用，1-启用")
    private Integer status;
} 