package cn.tianxing.cloud.module.system.controller.admin.dept.vo.dept;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

@Schema(description = "管理后台 - 部门树列表 Request VO")
@Data
@Accessors(chain = true)
public class DeptTreeListVo {

    @Schema(description = "部门ID")
    private Long id;

    @Schema(description = "部门名称")
    private String name;

    @Schema(description = "父部门ID")
    private Long parentId;

    @Schema(description = "显示顺序")
    private Integer sort;

    @Schema(description = "部门子级列表")
    private List<DeptTreeListVo> children = new ArrayList<>();
}
