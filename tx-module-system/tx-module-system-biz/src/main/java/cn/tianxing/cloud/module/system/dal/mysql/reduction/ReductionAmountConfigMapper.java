package cn.tianxing.cloud.module.system.dal.mysql.reduction;

import cn.tianxing.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.tianxing.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.tianxing.cloud.module.system.dal.dataobject.reduction.ReductionAmountConfigDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 减免金额配置 Mapper
 */
@Mapper
public interface ReductionAmountConfigMapper extends BaseMapperX<ReductionAmountConfigDO> {

    /**
     * 根据机构ID和期数查询减免金额配置
     *
     * @param mechanismId 机构ID
     * @param period 期数
     * @return 减免金额配置
     */
    default ReductionAmountConfigDO selectByMechanismIdAndPeriod(Long mechanismId, Long period) {
        return selectOne(new LambdaQueryWrapperX<ReductionAmountConfigDO>()
                .eq(ReductionAmountConfigDO::getMechanismId, mechanismId)
                .eq(ReductionAmountConfigDO::getPeriod, period));
    }
} 