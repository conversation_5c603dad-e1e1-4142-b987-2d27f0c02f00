package cn.tianxing.cloud.module.system.controller.admin.mechanism.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 机构 Base VO，提供给添加、修改、详细的子 VO 使用
 * 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class MechanismBaseVO {

    @Schema(description = "机构类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "企业")
    private String mechanismType;

    @Schema(description = "证件类型", example = "身份证")
    private String idType;

    @Schema(description = "机构名称/个人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "天行健科技有限公司")
    private String mechanismName;

    @Schema(description = "会员id", example = "1024")
    private Long memberId;

    @Schema(description = "统一社会信用代码/个人身份证号", example = "91110000802100433B")
    private String creditCode;

    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;

    @Schema(description = "联系座机", example = "010-12345678")
    private String contactTelephone;

    @Schema(description = "电子邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "职业", example = "工程师")
    private String profession;

    @Schema(description = "职务", example = "高级工程师")
    private String jobTitle;

    @Schema(description = "单位名称", example = "天行健科技有限公司")
    private String companyName;

    @Schema(description = "法定代表人", example = "张三")
    private String legalPerson;

    @Schema(description = "法人联系方式", example = "***********")
    private String legalPersonPhone;

    @Schema(description = "法人证件号", example = "110101199001011234")
    private String legalPersonIdCard;

    @Schema(description = "开户行联行号", example = "************")
    private String bankUnionCode;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "注册地址/户籍地址", example = "北京市")
    private String registerAddress;

    @Schema(description = "注册详细地址/户籍详细地址", example = "朝阳区xxx街道xxx号")
    private String registerDetailAddress;

    @Schema(description = "办公地址/联系地址", example = "北京市")
    private String officeAddress;

    @Schema(description = "办公详细地址/联系详细地址", example = "海淀区xxx街道xxx号")
    private String officeDetailAddress;
    
    @Schema(description = "营业执照", example = "http://example.com/license.jpg")
    private String businessLicense;
    
    @Schema(description = "中证码", example = "123456")
    private String zhongzhengCode;
} 