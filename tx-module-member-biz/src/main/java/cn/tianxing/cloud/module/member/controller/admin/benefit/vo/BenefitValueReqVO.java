package cn.tianxing.cloud.module.member.controller.admin.benefit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 权益值详情 Request VO
 */
@Data
public class BenefitValueReqVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "权益id")
    private Long benefitId;

    @Schema(description = "权益值")
    private BigDecimal benefitValue;
} 