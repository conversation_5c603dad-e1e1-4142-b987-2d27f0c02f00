package cn.tianxing.cloud.module.report.dal.dataobject.survey;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * scvt报告数据内容
 * @TableName survey_scvt
 */
@TableName(value ="survey_scvt")
@Data
public class SurveyScvtDO implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 编码
     */
    private String code;

    /**
     * 画像昵称
     */
    private String nickName;

    /**
     * 画像解读
     */
    private String portrait;

    /**
     * 概述
     */
    private String overview;

    /**
     * 核心目标
     */
    private String coreGoals;
    private String coreGoalsDescribe;


    /**
     * 长期目标
     */
    private String longTermGoals;
    private String longTermGoalsDescribe;
    private String detailDescribe;

    /**
     * 策略价值锚点
     */
    private String strategicValue;

    /**
     * 适配案例
     */
    private String adaptationCase;

    /**
     * 关键词
     */
    private String keyWords;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 删除标志（0：未删除，1：已删除）
     */
    private Integer deleted;

    /**
     * 租户ID
     */
    private Long tenantId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SurveyScvtDO other = (SurveyScvtDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCode() == null ? other.getCode() == null : this.getCode().equals(other.getCode()))
            && (this.getNickName() == null ? other.getNickName() == null : this.getNickName().equals(other.getNickName()))
            && (this.getPortrait() == null ? other.getPortrait() == null : this.getPortrait().equals(other.getPortrait()))
            && (this.getOverview() == null ? other.getOverview() == null : this.getOverview().equals(other.getOverview()))
            && (this.getCoreGoals() == null ? other.getCoreGoals() == null : this.getCoreGoals().equals(other.getCoreGoals()))
            && (this.getLongTermGoals() == null ? other.getLongTermGoals() == null : this.getLongTermGoals().equals(other.getLongTermGoals()))
            && (this.getStrategicValue() == null ? other.getStrategicValue() == null : this.getStrategicValue().equals(other.getStrategicValue()))
            && (this.getAdaptationCase() == null ? other.getAdaptationCase() == null : this.getAdaptationCase().equals(other.getAdaptationCase()))
            && (this.getKeyWords() == null ? other.getKeyWords() == null : this.getKeyWords().equals(other.getKeyWords()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getUpdater() == null ? other.getUpdater() == null : this.getUpdater().equals(other.getUpdater()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getTenantId() == null ? other.getTenantId() == null : this.getTenantId().equals(other.getTenantId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
        result = prime * result + ((getNickName() == null) ? 0 : getNickName().hashCode());
        result = prime * result + ((getPortrait() == null) ? 0 : getPortrait().hashCode());
        result = prime * result + ((getOverview() == null) ? 0 : getOverview().hashCode());
        result = prime * result + ((getCoreGoals() == null) ? 0 : getCoreGoals().hashCode());
        result = prime * result + ((getLongTermGoals() == null) ? 0 : getLongTermGoals().hashCode());
        result = prime * result + ((getStrategicValue() == null) ? 0 : getStrategicValue().hashCode());
        result = prime * result + ((getAdaptationCase() == null) ? 0 : getAdaptationCase().hashCode());
        result = prime * result + ((getKeyWords() == null) ? 0 : getKeyWords().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getUpdater() == null) ? 0 : getUpdater().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getTenantId() == null) ? 0 : getTenantId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", code=").append(code);
        sb.append(", nickName=").append(nickName);
        sb.append(", portrait=").append(portrait);
        sb.append(", overview=").append(overview);
        sb.append(", coreGoals=").append(coreGoals);
        sb.append(", longTermGoals=").append(longTermGoals);
        sb.append(", strategicValue=").append(strategicValue);
        sb.append(", adaptationCase=").append(adaptationCase);
        sb.append(", keyWords=").append(keyWords);
        sb.append(", createdTime=").append(createTime);
        sb.append(", updatedTime=").append(updateTime);
        sb.append(", creator=").append(creator);
        sb.append(", updater=").append(updater);
        sb.append(", deleted=").append(deleted);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}