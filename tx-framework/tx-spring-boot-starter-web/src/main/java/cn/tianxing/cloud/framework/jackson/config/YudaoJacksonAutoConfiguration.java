package cn.tianxing.cloud.framework.jackson.config;

import cn.hutool.core.collection.CollUtil;
import cn.tianxing.cloud.framework.common.util.json.JsonUtils;
import cn.tianxing.cloud.framework.common.util.json.databind.NumberSerializer;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@AutoConfiguration
@Slf4j
public class YudaoJacksonAutoConfiguration {

    @Bean
    @SuppressWarnings("InstantiationOfUtilityClass")
    public JsonUtils jsonUtils(List<ObjectMapper> objectMappers) {
        // 1.1 创建 SimpleModule 对象
        SimpleModule simpleModule = new SimpleModule();
        simpleModule
                // 新增 Long 类型序列化规则，数值超过 2^53-1，在 JS 会出现精度丢失问题，因此 Long 自动序列化为字符串类型
                .addSerializer(Long.class, NumberSerializer.INSTANCE)
                .addSerializer(Long.TYPE, NumberSerializer.INSTANCE)
//                .addSerializer(LocalDate.class, LocalDateSerializer.INSTANCE)
                .addSerializer(LocalDate.class, new JsonSerializer<LocalDate>() {
                    @Override
                    public void serialize(LocalDate value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                        if (value != null) {
                            gen.writeString(value.atStartOfDay(java.time.ZoneId.of("Asia/Shanghai"))
                                    .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                        }
                    }
                })
                // .addDeserializer(LocalDate.class, LocalDateDeserializer.INSTANCE)
                .addDeserializer(LocalDate.class, new JsonDeserializer<LocalDate>() {
                    @Override
                    public LocalDate deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                        String dateStr = p.getText();
                        if (dateStr != null && !dateStr.isEmpty()) {
                            return LocalDate.parse(dateStr, 
                                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                                    .atStartOfDay(java.time.ZoneId.of("Asia/Shanghai"))
                                    .toLocalDate();
                        }
                        return null;
                    }
                })
                .addSerializer(LocalTime.class, LocalTimeSerializer.INSTANCE)
                .addDeserializer(LocalTime.class, LocalTimeDeserializer.INSTANCE)
                // 新增 LocalDateTime 序列化、反序列化规则，使用 Long 时间戳
//                 .addSerializer(LocalDateTime.class, TimestampLocalDateTimeSerializer.INSTANCE)
                .addSerializer(LocalDateTime.class, new JsonSerializer<LocalDateTime>() {
                    @Override
                    public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                        if (value != null) {
                            gen.writeString(value.atZone(java.time.ZoneId.of("Asia/Shanghai"))
                                    .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        }
                    }
                })
                //.addDeserializer(LocalDateTime.class, TimestampLocalDateTimeDeserializer.INSTANCE)
                .addDeserializer(LocalDateTime.class, new JsonDeserializer<LocalDateTime>() {
                    @Override
                    public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
                        String dateStr = p.getText();
                        if (dateStr != null && !dateStr.isEmpty()) {
                            return LocalDateTime.parse(dateStr, 
                                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                                    .atZone(java.time.ZoneId.of("Asia/Shanghai"))
                                    .toLocalDateTime();
                        }
                        return null;
                    }
                })
                .addSerializer(BigDecimal.class, new JsonSerializer<>() {
                    @Override
                    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                        if (value != null) {
                            gen.writeString(value.setScale(2, RoundingMode.HALF_UP).toString());
                        }
                    }
                });
        // 1.2 注册到 objectMapper
        objectMappers.forEach(objectMapper -> objectMapper.registerModule(simpleModule));

        // 2. 设置 objectMapper 到 JsonUtils
        JsonUtils.init(CollUtil.getFirst(objectMappers));
        log.info("[init][初始化 JsonUtils 成功]");
        return new JsonUtils();
    }


}
