services:
  gateway:
    build:
      context: ./tx-cloud-base/tx-gateway   #Dockerfile 所在的路径
      dockerfile: Dockerfile      #Dockerfile 文件名
    ports:
      - "48080:48080"
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=tangpanhui

  crm:
    build:
      context: ./tx-module-crm/tx-module-crm-biz
      dockerfile: Dockerfile
    ports:
      - "48089:48089"
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=tangpanhui

  member:
    build:
      context: ./tx-module-member/tx-module-member-biz
      dockerfile: Dockerfile
    ports:
      - "48087:48087"
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=tangpanhui

  infra:
    build:
      context: ./tx-cloud-base/tx-module-infra/tx-module-infra-biz
      dockerfile: Dockerfile
    ports:
      - "48082:48082"
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=tangpanhui
      - JAVA_OPTS=-Xms512m -Xmx512m
      - TZ=Asia/Shanghai

  system:
    build:
      context: ./tx-cloud-base/tx-module-system/tx-module-system-biz
      dockerfile: Dockerfile
    ports:
      - "48081:48081"
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=tangpanhui

networks:
  default:
    name: tph_default
    external: true

